import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import {
  AutonomousAgentsService,
  OpenAPI,
  RecommendationPublic,
} from '@/client';
import { MessageDisplayComponentPublic } from '@/client/types.gen';
import { Dashboard, Report } from '@/client/types.gen';
import { Message as ChatMessage } from '@/components/chat/types';
import { useUserContext } from '@/features/user/provider/user-provider';
import { toast, useToast } from '@/hooks/use-toast';
import { convertToUIMessage } from '@/lib/message-converters';
import { generateId } from '@/lib/utils';
import type { components } from '@/openapi-ts/gens';
import { APIMessage, AgentThought } from '@/types/agent';
import { useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';

// Audio notification utility
const playNotificationSound = () => {
  try {
    const audio = new Audio('/sounds/notification.mp3');
    audio.volume = 0.5; // Set volume to 50%
    audio.play().catch((error) => {
      console.warn('Failed to play notification sound:', error);
    });
  } catch (error) {
    console.warn('Audio notification not available:', error);
  }
};

// Event buffering system for SSE events
interface SSEEvent {
  type: string;
  content: any;
  message_id?: string;
  namespace?: string;
  name?: string;
  timestamp: number;
}

class EventBuffer {
  private buffer: SSEEvent[] = [];
  private readonly maxSize: number;
  private readonly maxMemoryMB: number;

  constructor(maxSize: number = 1000, maxMemoryMB: number = 10) {
    this.maxSize = maxSize;
    this.maxMemoryMB = maxMemoryMB;
  }

  add(event: SSEEvent): void {
    // Add timestamp if not present
    if (!event.timestamp) {
      event.timestamp = Date.now();
    }

    // Check memory usage (rough estimation)
    const currentMemoryMB = this.estimateMemoryUsage();
    if (currentMemoryMB > this.maxMemoryMB) {
      console.warn(
        `Event buffer memory usage (${currentMemoryMB}MB) exceeds limit (${this.maxMemoryMB}MB), clearing older events`,
      );
      this.clearOldEvents();
    }

    // Remove oldest events if buffer is full
    while (this.buffer.length >= this.maxSize) {
      this.buffer.shift();
    }

    this.buffer.push(event);
  }

  flush(): SSEEvent[] {
    const events = [...this.buffer];
    this.buffer = [];
    return events;
  }

  getSize(): number {
    return this.buffer.length;
  }

  clear(): void {
    this.buffer = [];
  }

  private estimateMemoryUsage(): number {
    // Rough estimation: 1KB per event on average
    return (this.buffer.length * 1024) / (1024 * 1024); // Convert to MB
  }

  private clearOldEvents(): void {
    // Remove events older than 5 minutes
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    this.buffer = this.buffer.filter(
      (event) => event.timestamp > fiveMinutesAgo,
    );
  }
}

// Connection health monitoring system
interface ConnectionHealth {
  isConnected: boolean;
  lastEventTime: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

const useConnectionHealth = () => {
  const [health, setHealth] = useState<ConnectionHealth>({
    isConnected: true,
    lastEventTime: Date.now(),
    reconnectAttempts: 0,
    maxReconnectAttempts: 3,
  });

  const updateLastEventTime = useCallback(() => {
    setHealth((prev) => ({
      ...prev,
      lastEventTime: Date.now(),
      isConnected: true,
      reconnectAttempts: 0, // Reset on successful event
    }));
  }, []);

  const markDisconnected = useCallback(() => {
    setHealth((prev) => ({
      ...prev,
      isConnected: false,
    }));
  }, []);

  const incrementReconnectAttempts = useCallback(() => {
    setHealth((prev) => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1,
    }));
  }, []);

  const resetConnection = useCallback(() => {
    setHealth((prev) => ({
      ...prev,
      isConnected: true,
      reconnectAttempts: 0,
      lastEventTime: Date.now(),
    }));
  }, []);

  // Monitor connection health
  useEffect(() => {
    const healthCheckInterval = setInterval(() => {
      setHealth((prev) => {
        const timeSinceLastEvent = Date.now() - prev.lastEventTime;
        const connectionTimeout = 30000; // 30 seconds

        if (timeSinceLastEvent > connectionTimeout && prev.isConnected) {
          console.warn(
            'Connection appears to be stale, marking as disconnected',
          );
          return {
            ...prev,
            isConnected: false,
          };
        }

        return prev;
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(healthCheckInterval);
  }, []);

  return {
    health,
    updateLastEventTime,
    markDisconnected,
    incrementReconnectAttempts,
    resetConnection,
  };
};

type InterruptMessage = {
  value: string;
};

const createInterruptHandlers = (
  formattedMessage: InterruptMessage,
  conversationId: string,
  setInterruptConfirmation: (confirmation: any) => void,
  setIsStreaming: (isStreaming: boolean) => void,
  toast: any,
  processMessageStream: (
    reader: ReadableStreamDefaultReader<Uint8Array>,
    streamOptions?: any,
  ) => Promise<void>,
  abortControllerRef: React.MutableRefObject<AbortController | null>,
  streamOptions?: {
    onRecommendations?: (recommendations: RecommendationPublic[]) => void;
    resourceId?: string;
  },
) => {
  return {
    message: formattedMessage,
    onConfirm: async () => {
      setInterruptConfirmation(null);

      try {
        setIsStreaming(true);

        // Create new AbortController for resumed stream
        if (abortControllerRef.current) {
          abortControllerRef.current = null;
        }
        abortControllerRef.current = new AbortController();

        const response = await chatStream(
          {
            conversationId: conversationId,
            message: { content: '', resume: true, approve: true },
          },
          abortControllerRef.current,
        );

        if (!response.body) throw new Error('No response body received');
        await processMessageStream(response.body.getReader(), streamOptions);
      } catch (error) {
        console.error('Resume stream error:', error);
        if (!(error instanceof Error && error.name === 'AbortError')) {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'Failed to resume stream',
          });
        }
      } finally {
        setIsStreaming(false);
        if (abortControllerRef.current) {
          abortControllerRef.current = null;
        }
      }
    },
    onCancel: async (cancelMessage: string) => {
      setInterruptConfirmation(null);

      try {
        setIsStreaming(true);

        // Create new AbortController for resumed stream
        if (abortControllerRef.current) {
          abortControllerRef.current = null;
        }
        abortControllerRef.current = new AbortController();

        const response = await chatStream(
          {
            conversationId: conversationId,
            message: { content: cancelMessage, resume: true, approve: false },
          },
          abortControllerRef.current,
        );

        if (!response.body) throw new Error('No response body received');
        await processMessageStream(response.body.getReader(), streamOptions);
      } catch (error) {
        console.error('Resume stream error:', error);
        if (!(error instanceof Error && error.name === 'AbortError')) {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'Failed to resume stream',
          });
        }
      } finally {
        setIsStreaming(false);
        if (abortControllerRef.current) {
          abortControllerRef.current = null;
        }
      }
    },
  };
};

export type InterruptConfirmation = {
  message: InterruptMessage;
  onConfirm: () => void;
  onCancel: (cancelMessage: string) => void;
};

interface ChatStreamParams {
  conversationId: string | null;
  message: {
    content: string;
    resume: boolean;
    approve: boolean;
    message_id?: string;
    attachment_ids?: string[];
    resource_id?: string;
  };
}

// custom function to stream chat messages with an agent
/**
 * Chat Stream
 * Stream chat messages with an agent
 * @param data The data for the request.
 * @param data.conversationId
 * @param data.message
 * @param abortController Optional AbortController to enable stream termination
 * @returns Response Successful Response
 * @throws ApiError
 */
export async function chatStream(
  { conversationId, message }: ChatStreamParams,
  abortController?: AbortController,
): Promise<Response> {
  if (!OpenAPI.BASE) {
    throw new Error('API base URL is not configured');
  }

  let tokenValue = await OpenAPI.TOKEN;
  if (typeof tokenValue === 'function') {
    tokenValue = await tokenValue({
      method: 'POST',
      url: '/api/v1/autonomous-agents/chat/stream',
    });
  }

  if (!tokenValue) {
    throw new Error('Authentication token is required');
  }

  // Sanitize the token
  const sanitizedToken = String(tokenValue)
    .trim()
    .replace(/[\r\n]+/g, '');

  try {
    // Validate the URL construction
    const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
    const url = new URL(
      conversationId
        ? `${baseUrl}/api/v1/autonomous-agents/chat/${conversationId}/stream`
        : `${baseUrl}/api/v1/autonomous-agents/chat/stream`,
    );

    // Check if abort was already requested
    if (abortController?.signal.aborted) {
      throw new Error('Aborted');
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${sanitizedToken}`,
      },
      body: JSON.stringify(message),
      signal: abortController?.signal, // Add abort signal to fetch request
    });

    if (!response.ok && response.status == 403) {
      toast({
        variant: 'destructive',
        title: 'MISSING CREDENTIALS!',
        description: 'Please configure workspace credentials!',
      });

      return response;
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  } catch (error) {
    if (error instanceof Error) {
      // Add more context to the error message
      if (error.name === 'AbortError') {
        throw error; // Re-throw abort errors to be handled by caller
      }
      if (error.message.includes('Invalid value')) {
        console.error('Detailed request info:', {
          baseUrl: OpenAPI.BASE,
          conversationId: conversationId,
          tokenLength: tokenValue?.length,
        });
      }
      throw error;
    }
    throw new Error('Failed to make chat stream request');
  }
}

export function useMessageStream(
  conversationId: string | null,
  options?: {
    onRecommendations?: (recommendations: RecommendationPublic[]) => void;
    resourceId?: string;
    onConversationCreated?: (conversationId: string) => void;
  },
) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessages, setStreamingMessages] = useState<ChatMessage[]>([]);
  const [interruptConfirmation, setInterruptConfirmation] =
    useState<InterruptConfirmation | null>(null);
  const [streamStartTime, setStreamStartTime] = useState<number | null>(null);

  // Thinking content
  const [thinkingContent, setThinkingContent] = useState<string | null>(null);

  // Planning content
  const [planningContent, setPlanningContent] = useState<any>(null);

  const currentGlobalMessageRef = useRef<APIMessage | null>(null);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentReport, setCurrentReport] = useState<Report | null>(null);
  const [currentDashboard, setCurrentDashboard] = useState<Dashboard | null>(
    null,
  );

  // Track if conversation ID came from streaming to prevent loading messages
  const streamingConversationIds = useRef(new Set<string>());

  // MessagePublicList fields
  const [conversationResourceId, setConversationResourceId] = useState<
    string | null
  >(null);
  const [hasReport, setHasReport] = useState<boolean>(false);
  const [hasDashboard, setHasDashboard] = useState<boolean>(false);

  const { user } = useUserContext();

  // Initialize event buffer and connection health monitoring
  const eventBuffer = useMemo(() => new EventBuffer(1000, 10), []);
  const { health, updateLastEventTime, markDisconnected, resetConnection } =
    useConnectionHealth();

  // Expose connection health status
  const connectionStatus = {
    isConnected: health.isConnected,
    lastEventTime: health.lastEventTime,
    reconnectAttempts: health.reconnectAttempts,
  };

  // Helper to update both state and ref
  const updateCurrentGlobalMessage = (msg: APIMessage | null) => {
    currentGlobalMessageRef.current = msg;
  };

  // Load messages from API when conversation ID changes
  useEffect(() => {
    if (conversationId) {
      // Check if we're already streaming for this conversation or have messages for it
      // If so, don't reload messages (this prevents clearing messages when URL is updated)
      if (
        isStreaming &&
        currentGlobalMessageRef.current?.conversation_id === conversationId
      ) {
        return;
      }

      // Check if we already have messages for this conversation from streaming
      // This prevents reloading when the URL is updated from a streaming conversation_id event
      if (
        streamingMessages.length > 0 &&
        currentGlobalMessageRef.current?.conversation_id === conversationId
      ) {
        return;
      }

      // Skip loading if this conversation ID came from streaming
      if (streamingConversationIds.current.has(conversationId)) {
        return;
      }

      setInterruptConfirmation(null);
      setCurrentReport(null); // Clear report when conversation changes
      setCurrentDashboard(null); // Clear dashboard when conversation changes

      // Clear MessagePublicList fields when conversation changes
      setConversationResourceId(null);
      setHasReport(false);
      setHasDashboard(false);

      eventBuffer.clear(); // Clear buffer for new conversation
      resetConnection(); // Reset connection health

      // Clean up streaming conversation IDs for old conversations (but keep current one)
      const currentStreamingIds = new Set<string>();
      if (streamingConversationIds.current.has(conversationId)) {
        currentStreamingIds.add(conversationId);
      }
      streamingConversationIds.current = currentStreamingIds;

      // Skip loading messages if this is a newly created conversation
      // We can detect this by checking if the conversation ID is in sessionStorage
      const isNewlyCreated = sessionStorage.getItem(
        `new-conversation-${conversationId}`,
      );
      if (isNewlyCreated) {
        // Clear the flag after using it
        sessionStorage.removeItem(`new-conversation-${conversationId}`);
        setIsLoadingMessages(false);
        setStreamingMessages([]);
        return;
      }

      const loadMessages = async () => {
        setIsLoadingMessages(true);
        try {
          const response = (await AutonomousAgentsService.getMessagesHistory({
            conversationId: conversationId,
          })) as components['schemas']['MessagePublicList'];

          // Extract MessagePublicList fields
          setConversationResourceId(response.resource_id || null);
          setHasReport(response.has_report || false);
          setHasDashboard(response.has_dashboard || false);

          // Check for active stream and attempt reconnection
          if (response.is_streaming_active && response.last_stream_position !== undefined) {
            console.log(`Active stream detected for conversation ${conversationId}, attempting reconnection from position ${response.last_stream_position}`);

            try {
              // Call the reconnection API endpoint
              const reconnectResponse = await fetch(
                `/api/v1/autonomous-agents/chat/${conversationId}/reconnect-stream?last_position=${response.last_stream_position}`,
                {
                  method: 'GET',
                  headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                  },
                  signal: abortControllerRef.current?.signal,
                }
              );

              if (reconnectResponse.ok && reconnectResponse.body) {
                console.log('Successfully reconnected to stream, processing events...');
                setIsStreaming(true);
                resetConnection();

                // Process the reconnected stream
                await processMessageStream(reconnectResponse.body.getReader(), {
                  onConversationCreated: options?.onConversationCreated,
                  onRecommendations: options?.onRecommendations,
                  resourceId: response.resource_id || undefined,
                });
              } else if (reconnectResponse.status === 404) {
                // No active stream found, this is normal
                console.log('No active stream found for reconnection');
              } else {
                console.warn('Failed to reconnect to stream:', reconnectResponse.statusText);
              }
            } catch (error) {
              if (error instanceof Error && error.name === 'AbortError') {
                // Reconnection was aborted, this is normal
                console.log('Stream reconnection aborted');
              } else {
                console.error('Error during stream reconnection:', error);
              }
            }
          }

          const messages =
            response.messages?.map((message) => {
              if (message.is_interrupt) {
                const interruptContent = message.interrupt_message;
                const formattedMessage = {
                  value: interruptContent || '',
                };
                setInterruptConfirmation(
                  createInterruptHandlers(
                    formattedMessage,
                    conversationId,
                    setInterruptConfirmation,
                    setIsStreaming,
                    toast,
                    processMessageStream,
                    abortControllerRef,
                  ),
                );
              }
              return convertToUIMessage(message);
            }) || [];
          const lastMessage =
            response.messages?.[response.messages?.length - 1];
          if (lastMessage) {
            updateCurrentGlobalMessage(lastMessage as unknown as APIMessage);
          }
          setStreamingMessages(messages);
        } catch (error) {
          console.error('Error loading messages:', error);
          markDisconnected(); // Mark as disconnected on error
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'Failed to load message history',
          });
        } finally {
          setIsLoadingMessages(false);
        }
      };
      loadMessages();
    } else {
      // Clean up when no conversation is selected
      eventBuffer.clear();
      streamingConversationIds.current.clear();
      setStreamingMessages([]);
      setInterruptConfirmation(null);
      setCurrentReport(null); // Clear report when no conversation is selected
      setCurrentDashboard(null); // Clear dashboard when no conversation is selected
    }
  }, [conversationId, eventBuffer, resetConnection, markDisconnected, toast]);

  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (abortControllerRef.current) {
        try {
          abortControllerRef.current.abort();
        } catch (error) {
          console.warn('Error aborting controller during cleanup:', error);
        }
        abortControllerRef.current = null;
      }

      // Clear event buffer
      eventBuffer.clear();

      // Clear streaming conversation IDs
      streamingConversationIds.current.clear();

      // Reset refs to prevent memory leaks
      currentGlobalMessageRef.current = null;
    };
  }, [eventBuffer]);

  const processMessageStream = async (
    reader: ReadableStreamDefaultReader<Uint8Array>,
    streamOptions?: {
      onRecommendations?: (recommendations: RecommendationPublic[]) => void;
      resourceId?: string;
      onConversationCreated?: (conversationId: string) => void;
    },
  ) => {
    const decoder = new TextDecoder();
    let currentMessage: APIMessage | null =
      currentGlobalMessageRef.current || null;
    let readerCancelled = false;

    // --- Global position counters ---
    let currentAgentThought: AgentThought | null =
      currentMessage?.agent_thoughts?.[
        currentMessage?.agent_thoughts?.length - 1
      ] || null;
    let globalPosition = currentAgentThought?.position || 0;
    // --- End global position counters ---

    // Function to cancel the reader properly
    const cancelReader = async () => {
      if (!readerCancelled) {
        try {
          await reader.cancel();
          readerCancelled = true;
        } catch (e) {
          console.error('Error cancelling reader:', e);
        }
      }
    };

    // Error recovery function
    const handleStreamError = (error: any, context: string) => {
      console.error(`Error in ${context}:`, error);

      // Mark connection as unhealthy
      markDisconnected();

      // Log error to event buffer for debugging
      const errorEvent: SSEEvent = {
        type: 'stream_error',
        content: `${context}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
      };
      eventBuffer.add(errorEvent);

      // Show user-friendly error message
      toast({
        variant: 'destructive',
        title: 'Stream Error',
        description: `An error occurred while processing the stream. Please try again.`,
      });

      // Ensure streaming state is reset
      setIsStreaming(false);
    };

    let buffer = '';
    const MAX_BUFFER_SIZE = 1024 * 1024; // 1MB limit for string buffer

    try {
      while (true) {
        try {
          // Check before read if stream has been aborted
          if (
            !abortControllerRef.current ||
            abortControllerRef.current.signal.aborted
          ) {
            await cancelReader();
            break;
          }

          const { value, done } = await reader.read();
          if (done) {
            break;
          }

          // Check if stream is still active - if not, break out early
          if (
            !abortControllerRef.current ||
            abortControllerRef.current.signal.aborted
          ) {
            await cancelReader();
            break;
          }

          const decoded = decoder.decode(value, { stream: true });

          // Check buffer size before adding new data
          if (buffer.length + decoded.length > MAX_BUFFER_SIZE) {
            console.warn(
              `Buffer size limit (${MAX_BUFFER_SIZE} bytes) reached, clearing buffer`,
            );
            buffer = decoded; // Start fresh with new data
          } else {
            buffer += decoded;
          }

          // Split on double newlines which separate SSE events (part of SSE specification)
          const events = buffer.split('\n\n');

          // Save the last incomplete event back to buffer
          buffer = events.pop() || '';

          for (const event of events) {
            const lines = event.split('\n');

            for (const line of lines) {
              try {
                let content = null;

                // Parse proper SSE format: "data: {json}" (part of SSE specification)
                if (line.startsWith('data: ')) {
                  const jsonData = line.slice(6);
                  try {
                    content = JSON.parse(jsonData);
                  } catch (e) {
                    console.error('Error parsing SSE JSON:', e);
                    continue;
                  }
                } else if (line.trim() === '') {
                  continue;
                } else {
                  // For backward compatibility, try parsing as raw JSON
                  try {
                    content = JSON.parse(line);
                  } catch (e) {
                    console.error('Error parsing line as JSON:', line, e);
                    continue;
                  }
                }

                if (!content) continue;

                // Handle new nested event structure from Redis persistence
                // Backend now sends: {event: {type, content, etc}, timestamp, position}
                // We need to extract the actual event data
                let eventData = content;
                if (content.event && typeof content.event === 'object') {
                  // This is the new nested structure, extract the event
                  eventData = content.event;
                } else if (content.type) {
                  // This is the old flat structure, use as-is
                  eventData = content;
                } else {
                  // Unknown structure, skip
                  console.warn('Unknown event structure:', content);
                  continue;
                }

                // Update connection health on successful event
                updateLastEventTime();

                // Add event to buffer for monitoring
                const sseEvent: SSEEvent = {
                  type: eventData.type || 'unknown',
                  content: eventData.content,
                  message_id: eventData.message_id,
                  namespace: eventData.namespace,
                  name: eventData.name,
                  timestamp: content.timestamp || Date.now(),
                };
                eventBuffer.add(sseEvent);

                const role =
                  eventData.type != 'complete' ? eventData.namespace : '';

                // --- Handle conversation ID for new conversations ---
                if (eventData.type === 'conversation_id') {
                  // This is a new conversation, update the URL
                  const newConversationId = eventData.conversation_id;
                  if (
                    newConversationId &&
                    streamOptions?.onConversationCreated
                  ) {
                    // Mark this conversation ID as coming from streaming
                    streamingConversationIds.current.add(newConversationId);
                    streamOptions.onConversationCreated(newConversationId);
                  }
                  continue;
                }

                // --- Handle message ID ---
                if (
                  eventData.type === 'message_id' &&
                  (!currentMessage || currentMessage.id !== eventData.message_id)
                ) {
                  try {
                    currentMessage = {
                      id: eventData.message_id,
                      conversation_id: conversationId!,
                      content: '',
                      role: eventData.namespace,
                      created_at: Math.floor(Date.now() / 1000),
                      agent_thoughts: [],
                      display_components: [],
                    };
                    currentAgentThought = null;
                    setThinkingContent(null);

                    // IMMEDIATE UI UPDATE - Show agent message with role
                    const convertedMessage = convertToUIMessage(currentMessage);
                    setStreamingMessages((prev) => {
                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    updateCurrentGlobalMessage(currentMessage);

                    if (user?.id) {
                      queryClient.invalidateQueries({
                        queryKey: ['quota-info', user.id],
                      });
                    }
                  } catch (error) {
                    handleStreamError(error, 'message_id processing');
                    continue;
                  }
                }
                // --- AGENT THOUGHT MANAGEMENT LOGIC ---
                else if (
                  eventData.type === 'stream' ||
                  eventData.type === 'function_call' ||
                  eventData.type === 'function_stream'
                ) {
                  try {
                    if (!currentMessage) {
                      currentMessage = {
                        id: eventData.message_id || generateId(),
                        conversation_id: conversationId!,
                        content: '',
                        role: role,
                        created_at: Math.floor(Date.now() / 1000),
                        agent_thoughts: [],
                        display_components: [],
                      };
                    }
                    // If there is a current agent thought, update it
                    if (currentAgentThought) {
                      if (eventData.type === 'stream') {
                        currentAgentThought.thought += eventData.content || '';
                      } else if (eventData.type === 'function_call') {
                        currentAgentThought.tool = eventData.name;
                        currentAgentThought.tool_input = eventData.content || '';
                      } else if (eventData.type === 'function_stream') {
                        // Update tool input progressively for streaming function calls
                        currentAgentThought.tool = eventData.name;
                        currentAgentThought.tool_input = eventData.content || '';
                      }
                    } else {
                      // Create new agent thought
                      const thoughtId = generateId();
                      currentAgentThought = {
                        id: thoughtId,
                        message_id: currentMessage.id,
                        position: globalPosition,
                        thought:
                          eventData.type === 'stream'
                            ? eventData.content || ''
                            : '',
                        tool:
                          eventData.type === 'function_call' ||
                          eventData.type === 'function_stream'
                            ? eventData.name
                            : '',
                        tool_input:
                          eventData.type === 'function_call' ||
                          eventData.type === 'function_stream'
                            ? eventData.content || ''
                            : '',
                        observation: '',
                        created_at: Math.floor(Date.now() / 1000),
                      };
                      currentMessage.agent_thoughts.push(currentAgentThought);
                    }
                    // Update UI
                    const convertedMessage = convertToUIMessage(currentMessage);
                    setStreamingMessages((prev) => {
                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    updateCurrentGlobalMessage(currentMessage);
                  } catch (error) {
                    handleStreamError(
                      error,
                      'stream/function_call/function_stream processing',
                    );
                    continue;
                  }
                } else if (eventData.type === 'function_result') {
                  try {
                    // Update the current agent thought's observation
                    if (currentAgentThought) {
                      currentAgentThought.observation = eventData.content || '';
                    }
                    // Update UI
                    const convertedMessage = convertToUIMessage(currentMessage);
                    setStreamingMessages((prev) => {
                      const lastMessage = prev[prev.length - 1];
                      if (lastMessage?.id === convertedMessage.id) {
                        return [...prev.slice(0, -1), convertedMessage];
                      }
                      return [...prev, convertedMessage];
                    });
                    // Then create a new agent thought for the next phase
                    currentAgentThought = null;
                    globalPosition += 1;
                    updateCurrentGlobalMessage(currentMessage);
                  } catch (error) {
                    handleStreamError(error, 'function_result processing');
                    continue;
                  }
                } else if (eventData.type === 'display_component') {
                  try {
                    // Handle display component event like tables, charts, etc.
                    if (!currentMessage) {
                      currentMessage = {
                        id: eventData.message_id || generateId(),
                        conversation_id: conversationId!,
                        content: '',
                        role: role,
                        created_at: Math.floor(Date.now() / 1000),
                        agent_thoughts: [],
                        display_components: [],
                      };
                    }

                    if (!currentMessage.display_components) {
                      currentMessage.display_components = [];
                    }

                    // Check if this component already exists
                    const componentExists =
                      currentMessage.display_components.some(
                        (comp) => comp.id === eventData.content.id,
                      );

                    if (!componentExists) {
                      // Create enhanced display component with position tracking
                      const displayComponent: MessageDisplayComponentPublic = {
                        ...eventData.content,
                        position: ++globalPosition,
                        created_at:
                          eventData.content.created_at ||
                          new Date().toISOString(),
                      };

                      // Add to message
                      currentMessage.display_components.push(displayComponent);

                      // Update UI
                      const convertedMessage =
                        convertToUIMessage(currentMessage);
                      setStreamingMessages((prev) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === convertedMessage.id) {
                          return [...prev.slice(0, -1), convertedMessage];
                        }
                        return [...prev, convertedMessage];
                      });
                      currentAgentThought = null;
                      globalPosition += 1;
                      updateCurrentGlobalMessage(currentMessage);

                      // Handle recommendations table
                      if (
                        eventData.content.type === 'table' &&
                        streamOptions?.onRecommendations
                      ) {
                        // Extract and process recommendations from the table data
                        handleRecommendationsTable(
                          eventData.content,
                          streamOptions,
                        );
                      }
                    }
                  } catch (error) {
                    handleStreamError(error, 'display_component processing');
                    continue;
                  }
                } else if (eventData.type === 'chart_data') {
                  try {
                    // Handle chart data event
                    if (!currentMessage) {
                      currentMessage = {
                        id: eventData.message_id || generateId(),
                        conversation_id: conversationId!,
                        content: '',
                        role: role,
                        created_at: Math.floor(Date.now() / 1000),
                        agent_thoughts: [],
                        display_components: [],
                      };
                    }

                    if (!currentMessage.display_components) {
                      currentMessage.display_components = [];
                    }

                    // Check if this chart component already exists
                    const componentExists =
                      currentMessage.display_components.some(
                        (comp) => comp.id === eventData.content.id,
                      );

                    if (!componentExists) {
                      // Create a new display component for the chart
                      const chartComponent: MessageDisplayComponentPublic = {
                        id: eventData.content.id,
                        type: 'chart',
                        chart_type: eventData.content.chart_type,
                        title: null,
                        description: null,
                        data: eventData.content.data,
                        config: {},
                        position: currentMessage.display_components.length,
                        created_at: new Date().toISOString(),
                      };

                      currentMessage.display_components.push(chartComponent);
                      const convertedMessage =
                        convertToUIMessage(currentMessage);
                      setStreamingMessages((prev) => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage?.id === convertedMessage.id) {
                          return [...prev.slice(0, -1), convertedMessage];
                        }
                        return [...prev, convertedMessage];
                      });
                      currentAgentThought = null;
                      globalPosition += 1;
                      updateCurrentGlobalMessage(currentMessage);
                    }
                  } catch (error) {
                    handleStreamError(error, 'chart_data processing');
                    continue;
                  }
                } else if (
                  eventData.type === 'on_recommendation_generation_response'
                ) {
                  // Skip updating UI here since we already have the components
                  continue;
                } else if (eventData.type === 'on_report_generation_response') {
                  try {
                    // Handle report generation response
                    const reportData = eventData.content;
                    setCurrentReport(reportData);
                  } catch (error) {
                    console.error('❌ Error processing report:', error);
                    handleStreamError(
                      error,
                      'report generation response processing',
                    );
                    continue;
                  }
                } else if (
                  eventData.type === 'on_dashboard_generation_response'
                ) {
                  try {
                    // Handle dashboard generation response
                    const dashboardData = eventData.content;
                    setCurrentDashboard(dashboardData);
                  } catch (error) {
                    console.error('❌ Error processing dashboard:', error);
                    handleStreamError(
                      error,
                      'dashboard generation response processing',
                    );
                    continue;
                  }
                } else if (eventData.type === 'error') {
                  try {
                    toast({
                      variant: 'destructive',
                      title: 'Error',
                      description: eventData.content,
                    });
                    break;
                  } catch (error) {
                    handleStreamError(error, 'error message processing');
                    break;
                  }
                } else if (eventData.type === 'thinking') {
                  try {
                    // Handle thinking stream type
                    setThinkingContent(eventData.content || 'Working...');

                    // Don't create a new message, just update thinking state
                    // This allows UI to show "Working..." or actual thinking content
                  } catch (error) {
                    handleStreamError(error, 'thinking processing');
                    continue;
                  }
                } else if (eventData.type === 'planning') {
                  try {
                    // Handle planning event
                    setPlanningContent(eventData.content);
                    console.log('Planning content:', eventData.content);
                  } catch (error) {
                    handleStreamError(error, 'planning processing');
                    continue;
                  }
                } else if (eventData.type === 'interrupt') {
                  try {
                    // Handle interrupt event
                    const interruptContent =
                      typeof eventData.content === 'string'
                        ? eventData.content
                        : eventData.content?.value ||
                          'Would you like to proceed?';

                    const formattedMessage = {
                      value: interruptContent,
                      resumable: eventData.content?.resumable ?? true,
                      ns: eventData.content?.ns,
                      when: eventData.content?.when || new Date().toISOString(),
                    };

                    // Important: Stop the stream completely
                    if (abortControllerRef.current) {
                      abortControllerRef.current.abort();
                      abortControllerRef.current = null;
                    }

                    // Set streaming to false immediately
                    setIsStreaming(false);

                    // Set the confirmation dialog
                    setInterruptConfirmation(
                      createInterruptHandlers(
                        formattedMessage,
                        conversationId!,
                        setInterruptConfirmation,
                        setIsStreaming,
                        toast,
                        processMessageStream,
                        abortControllerRef,
                        streamOptions,
                      ),
                    );

                    // Break out of the while loop to stop streaming
                    break;
                  } catch (error) {
                    handleStreamError(error, 'interrupt processing');
                    break;
                  }
                }
                if (eventData.type === 'complete') {
                  try {
                    setIsStreaming(false);
                    setThinkingContent(null); // Clear thinking content when stream completes

                    // Play notification sound only if stream lasted more than 30 seconds
                    if (
                      streamStartTime &&
                      Date.now() - streamStartTime > 30000
                    ) {
                      playNotificationSound();
                    }

                    // Reset stream start time
                    setStreamStartTime(null);

                    // Invalidate quota query when stream completes
                    if (user?.id) {
                      queryClient.invalidateQueries({
                        queryKey: ['quota-info', user.id],
                      });
                    }
                    // Dispatch stream-end event
                  } catch (error) {
                    handleStreamError(error, 'complete processing');
                  }
                }
              } catch (error) {
                // Check if the error is related to abort
                if (
                  error instanceof Error &&
                  (error.name === 'AbortError' ||
                    error.message.includes('abort') ||
                    error.message.includes('cancel'))
                ) {
                  await cancelReader();
                  break;
                }

                // Use centralized error handling
                handleStreamError(error, 'message processing');

                // Continue to next line instead of breaking the entire stream
                continue;
              }
            }
          }
        } catch (error) {
          // Handle read loop errors
          if (
            error instanceof Error &&
            (error.name === 'AbortError' ||
              error.message.includes('abort') ||
              error.message.includes('cancel'))
          ) {
            console.log('Stream was aborted during read loop');
            await cancelReader();
            break;
          }

          // Use centralized error handling for other errors
          handleStreamError(error, 'stream read loop');

          // Try to recover by breaking out of the loop
          break;
        }
      }
    } catch (error) {
      // Handle fatal errors that escape the inner try-catch blocks
      handleStreamError(error, 'fatal stream error');
    } finally {
      // Ensure reader is canceled and reset streaming state
      try {
        await cancelReader();
      } catch (cleanupError) {
        console.error('Error during reader cleanup:', cleanupError);
      }

      // Always ensure streaming state is reset
      setIsStreaming(false);
      setThinkingContent(null); // Clear thinking content when stream stops
      setPlanningContent(null); // Clear planning content when stream stops

      // Clear any pending abort controller
      if (
        abortControllerRef.current &&
        typeof abortControllerRef.current.abort === 'function'
      ) {
        try {
          abortControllerRef.current.abort();
        } catch (abortError) {
          console.warn(
            'Error aborting controller in fatal error handler:',
            abortError,
          );
        }
        abortControllerRef.current = null;
      }
    }
  };

  // When sending a message, only use the stream for the new message data
  // Don't re-fetch the conversation history
  const handleSendMessage = useCallback(
    async (content: string, attachmentIds?: string[], resourceId?: string) => {
      if (abortControllerRef.current) {
        // Cancel any ongoing stream
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      try {
        // Set stream start time
        setStreamStartTime(Date.now());

        // Main message sending logic
        try {
          // Create a new abort controller for this stream
          const abortController = new AbortController();
          abortControllerRef.current = abortController;

          // Reset connection health for new message
          resetConnection();

          // Set streaming state to true
          setIsStreaming(true);

          // Add a temporary "sending" message from the user
          const tempId = uuidv4();
          const userMessage: ChatMessage = {
            id: tempId,
            role: 'user',
            content: content,
            timestamp: new Date(), // Use Date object instead of string
            attachmentIds: attachmentIds, // Include attachment IDs for immediate display
          };

          // Update streaming messages to include the user message
          setStreamingMessages((prev) => [...prev, userMessage]);

          // Stream the response
          let response: Response;

          try {
            response = await chatStream(
              {
                conversationId,
                message: {
                  content: content,
                  resume: false,
                  approve: true,
                  attachment_ids: attachmentIds,
                  resource_id: resourceId,
                },
              },
              abortController,
            );

            if (!response.body) {
              throw new Error('No response body received');
            }

            // Pass the options to processMessageStream
            await processMessageStream(response.body.getReader(), {
              onRecommendations: options?.onRecommendations,
              resourceId: options?.resourceId,
              onConversationCreated: options?.onConversationCreated,
            });
          } catch (error: any) {
            // Add type annotation
            console.error('Chat stream error:', error);
            markDisconnected(); // Mark connection as failed

            if (error instanceof Error && error.name === 'AbortError') {
              console.log('Stream was aborted by user');
            } else {
              try {
                // Create error message with proper type
                const errorMessage: ChatMessage = {
                  id: uuidv4(),
                  role: 'assistant',
                  content:
                    'Sorry, I encountered an error while processing your request. Please try again.',
                  timestamp: new Date(),
                };
                setStreamingMessages((prev) => [...prev, errorMessage]);

                // Log error to event buffer
                const errorEvent: SSEEvent = {
                  type: 'stream_error',
                  content: error.message || 'Unknown stream error',
                  timestamp: Date.now(),
                };
                eventBuffer.add(errorEvent);

                // Show user-friendly error toast
                toast({
                  variant: 'destructive',
                  title: 'Connection Error',
                  description:
                    'Failed to send message. Please check your connection and try again.',
                });
              } catch (recoveryError) {
                console.error('Error during error recovery:', recoveryError);
                // Last resort: just show a simple toast
                try {
                  toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                      'An unexpected error occurred. Please refresh the page.',
                  });
                } catch (toastError) {
                  console.error('Failed to show error toast:', toastError);
                }
              }
            }
          }
        } finally {
          // Ensure we clean up properly
          setIsStreaming(false);
          setThinkingContent(null); // Clear thinking content when stream stops
          setPlanningContent(null); // Clear planning content when stream stops

          if (abortControllerRef.current) {
            abortControllerRef.current = null;
          }
        }
      } catch (fatalError) {
        // Top-level error handler to prevent page crashes
        console.error('Fatal error in handleSendMessage:', fatalError);

        // Ensure streaming state is reset
        setIsStreaming(false);

        // Clear abort controller
        if (abortControllerRef.current) {
          try {
            (abortControllerRef.current as AbortController).abort();
          } catch (abortError) {
            console.warn(
              'Error aborting controller in fatal error handler:',
              abortError,
            );
          }
          abortControllerRef.current = null;
        }

        // Reset stream start time on fatal error
        setStreamStartTime(null);

        // Show user-friendly error message
        try {
          toast({
            variant: 'destructive',
            title: 'Critical Error',
            description:
              'A critical error occurred. Please refresh the page and try again.',
          });
        } catch (toastError) {
          console.error('Failed to show critical error toast:', toastError);
        }
      }
    },
    [conversationId, setStreamingMessages, options],
  );

  const clearStreamingMessages = useCallback(() => {
    setStreamingMessages([]);
  }, []);

  const stopStream = useCallback(() => {
    // First set streaming to false to update UI immediately
    setIsStreaming(false);
    setThinkingContent(null); // Clear thinking content when manually stopping

    // Reset stream start time
    setStreamStartTime(null);

    // Attempt to abort the stream controller if available
    if (abortControllerRef.current) {
      try {
        abortControllerRef.current.abort();
      } catch (err) {
        console.error('Error when aborting stream:', err);
      } finally {
        // Always clear the abort controller reference
        abortControllerRef.current = null;
        // Dispatch stream-end event when manually stopped
        window.dispatchEvent(new Event('stream-end'));
        // Play notification sound when stream is manually stopped
        playNotificationSound();
      }
    } else {
      console.warn('No abort controller available to stop stream');
      // Still dispatch stream-end event even if no controller
      window.dispatchEvent(new Event('stream-end'));
      // Play notification sound even if no controller
      playNotificationSound();
    }

    // Force any async processes to recognize the stream has stopped
    if (typeof window !== 'undefined') {
      // Use a timeout to ensure any pending UI updates are processed
      setTimeout(() => {
        if (isStreaming) {
          setIsStreaming(false);
        }
      }, 100);
    }
  }, [isStreaming]);

  function handleRecommendationsTable(
    tableContent: any,
    options?: {
      onRecommendations?: (recommendations: RecommendationPublic[]) => void;
      resourceId?: string;
    },
  ) {
    if (!options?.onRecommendations) return;

    const recommendations = tableContent.data.rows.map((row: any[]) => {
      // Parse potential savings with fallback to 0 if invalid
      let potentialSavings = 0;
      try {
        if (row[3] && typeof row[3] === 'string') {
          const rawValue = row[3].replace('$', '').replace(',', '');
          potentialSavings = parseFloat(rawValue);
          // If NaN, set to 0
          if (isNaN(potentialSavings)) potentialSavings = 0;
        } else if (typeof row[3] === 'number') {
          potentialSavings = row[3];
        }
      } catch (e) {
        console.warn('Error parsing potential savings:', e);
        potentialSavings = 0;
      }

      return {
        id: uuidv4(),
        type: row[0],
        title: row[1],
        description: row[2],
        potential_savings: potentialSavings,
        effort: row[4]?.toLowerCase() || 'medium',
        risk: row[5]?.toLowerCase() || 'low',
        status: row[8]?.toLowerCase() || 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        resource_id: options.resourceId || '',
      };
    });

    options.onRecommendations(recommendations);
  }

  return {
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    isLoadingMessages,
    connectionStatus,
    currentReport,
    currentDashboard,
    thinkingContent,
    planningContent,
    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
    // Debug information
    bufferSize: eventBuffer.getSize(),
    clearEventBuffer: () => eventBuffer.clear(),
  };
}
