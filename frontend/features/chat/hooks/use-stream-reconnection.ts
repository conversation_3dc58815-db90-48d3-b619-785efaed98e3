import { useCallback, useEffect, useRef } from 'react';

import { chatStream, OpenAPI } from '@/client';
import { toast } from 'sonner';

interface StreamReconnectionOptions {
  /**
   * Whether to enable automatic stream reconnection.
   * @default true
   */
  enabled?: boolean;
  /**
   * Callback fired when reconnection is attempted.
   */
  onReconnectionAttempt?: (
    conversationId: string,
    lastPosition: number,
  ) => void;
  /**
   * Callback fired when reconnection succeeds.
   */
  onReconnectionSuccess?: (conversationId: string) => void;
  /**
   * Callback fired when reconnection fails.
   */
  onReconnectionError?: (conversationId: string, error: Error) => void;
}

interface StreamStatus {
  is_streaming_active: boolean;
  stream_status: 'completed' | 'in_progress' | 'paused' | 'error';
  last_stream_position: number;
}

/**
 * Hook that provides stream reconnection functionality.
 * Detects when a conversation has an active stream and provides methods to reconnect.
 */
export function useStreamReconnection(options: StreamReconnectionOptions = {}) {
  const {
    enabled = true,
    onReconnectionAttempt,
    onReconnectionSuccess,
    onReconnectionError,
  } = options;

  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * Check if a conversation has an active stream that can be reconnected to.
   */
  const checkStreamStatus = useCallback(
    async (conversationId: string): Promise<StreamStatus | null> => {
      try {
        // The stream status is included in the message history response
        const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
        const url = new URL(
          `${baseUrl}/api/v1/autonomous-agents/messages/${conversationId}`
        );

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...OpenAPI.HEADERS,
          },
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch stream status: ${response.statusText}`,
          );
        }

        const data = await response.json();

        return {
          is_streaming_active: data.is_streaming_active || false,
          stream_status: data.stream_status || 'completed',
          last_stream_position: data.last_stream_position || 0,
        };
      } catch (error) {
        console.error('Error checking stream status:', error);
        return null;
      }
    },
    [],
  );

  /**
   * Reconnect to an active stream.
   */
  const reconnectToStream = useCallback(
    async (
      conversationId: string,
      lastPosition: number = 0,
      processStreamCallback?: (
        reader: ReadableStreamDefaultReader<Uint8Array>,
      ) => Promise<void>,
    ): Promise<boolean> => {
      if (!enabled) {
        return false;
      }

      try {
        onReconnectionAttempt?.(conversationId, lastPosition);

        // Create new AbortController for the reconnection
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        abortControllerRef.current = new AbortController();

        // Call the reconnection API endpoint using OpenAPI base URL
        const baseUrl = String(OpenAPI.BASE).trim().replace(/\/+$/, '');
        const reconnectUrl = new URL(
          `${baseUrl}/api/v1/autonomous-agents/chat/${conversationId}/reconnect-stream`
        );
        reconnectUrl.searchParams.set('last_position', String(lastPosition));

        const response = await fetch(reconnectUrl.toString(), {
          method: 'GET',
          headers: {
            Accept: 'text/event-stream',
            'Cache-Control': 'no-cache',
            ...OpenAPI.HEADERS,
          },
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          if (response.status === 404) {
            // No active stream found
            return false;
          }
          throw new Error(`Reconnection failed: ${response.statusText}`);
        }

        if (!response.body) {
          throw new Error('No response body received for stream reconnection');
        }

        // Process the stream if callback is provided
        if (processStreamCallback) {
          await processStreamCallback(response.body.getReader());
        }

        onReconnectionSuccess?.(conversationId);
        return true;
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          // Reconnection was aborted, this is normal
          return false;
        }

        console.error('Stream reconnection error:', error);
        const errorObj =
          error instanceof Error
            ? error
            : new Error('Unknown reconnection error');
        onReconnectionError?.(conversationId, errorObj);

        toast.error('Failed to reconnect to the active stream. Please refresh the page.');

        return false;
      }
    },
    [
      enabled,
      onReconnectionAttempt,
      onReconnectionSuccess,
      onReconnectionError,
    ],
  );

  /**
   * Attempt automatic reconnection if stream is active.
   */
  const attemptAutoReconnection = useCallback(
    async (
      conversationId: string,
      processStreamCallback?: (
        reader: ReadableStreamDefaultReader<Uint8Array>,
      ) => Promise<void>,
    ): Promise<boolean> => {
      if (!enabled || !conversationId) {
        return false;
      }

      const streamStatus = await checkStreamStatus(conversationId);

      if (!streamStatus || !streamStatus.is_streaming_active) {
        return false;
      }

      return await reconnectToStream(
        conversationId,
        streamStatus.last_stream_position,
        processStreamCallback,
      );
    },
    [enabled, checkStreamStatus, reconnectToStream],
  );

  /**
   * Abort any ongoing reconnection attempt.
   */
  const abortReconnection = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abortReconnection();
    };
  }, [abortReconnection]);

  return {
    checkStreamStatus,
    reconnectToStream,
    attemptAutoReconnection,
    abortReconnection,
  };
}
