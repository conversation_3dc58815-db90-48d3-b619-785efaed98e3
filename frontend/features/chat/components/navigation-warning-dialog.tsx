'use client';

import React, { PropsWithChildren, use<PERSON><PERSON>back, useEffect, useState } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useChatContext } from '@/features/chat/context/chat-context';

interface NavigationWarningDialogProps extends PropsWithChildren {
  /**
   * Whether to show the warning dialog when navigation is attempted during streaming.
   * If false, navigation will proceed without warning.
   */
  enabled?: boolean;
  /**
   * Custom message to display in the dialog. If not provided, a default message will be used.
   */
  customMessage?: string;
  /**
   * Callback fired when user confirms navigation despite active streaming.
   */
  onConfirmNavigation?: () => void;
  /**
   * Callback fired when user cancels navigation.
   */
  onCancelNavigation?: () => void;
}

export function NavigationWarningDialog({
  children,
  enabled = true,
  customMessage,
  onConfirmNavigation,
  onCancelNavigation,
}: NavigationWarningDialogProps) {
  const { isStreaming } = useChatContext();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<(() => void) | null>(null);

  const defaultMessage = customMessage || 
    "You have an active AI conversation in progress. If you leave now, the conversation will continue in the background, but you may miss important updates. Are you sure you want to continue?";

  const handleTriggerClick = useCallback((event: React.MouseEvent) => {
    // If streaming is not active or dialog is disabled, allow normal navigation
    if (!enabled || !isStreaming) {
      return;
    }

    // Prevent the default navigation
    event.preventDefault();
    event.stopPropagation();

    // Store the navigation action for later execution
    const target = event.currentTarget as HTMLElement;
    const href = target.getAttribute('href');
    const onClick = target.onclick;

    let navigationAction: (() => void) | null = null;

    if (href) {
      // Handle link navigation
      navigationAction = () => {
        window.location.href = href;
      };
    } else if (onClick) {
      // Handle onClick navigation
      navigationAction = () => {
        onClick.call(target, event as any);
      };
    }

    if (navigationAction) {
      setPendingNavigation(() => navigationAction);
      setIsDialogOpen(true);
    }
  }, [enabled, isStreaming]);

  const handleConfirm = useCallback(() => {
    setIsDialogOpen(false);
    
    // Execute the pending navigation
    if (pendingNavigation) {
      pendingNavigation();
      setPendingNavigation(null);
    }

    // Call the callback if provided
    onConfirmNavigation?.();
  }, [pendingNavigation, onConfirmNavigation]);

  const handleCancel = useCallback(() => {
    setIsDialogOpen(false);
    setPendingNavigation(null);
    
    // Call the callback if provided
    onCancelNavigation?.();
  }, [onCancelNavigation]);

  // Clone children and add click handler
  const enhancedChildren = React.cloneElement(children as React.ReactElement, {
    onClick: handleTriggerClick,
  });

  return (
    <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <AlertDialogTrigger asChild>
        {enhancedChildren}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Active Conversation in Progress</AlertDialogTitle>
          <AlertDialogDescription>
            {defaultMessage}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>
            Stay on Page
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirm}>
            Continue Navigation
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
